import { useMemo, useState } from 'react'
import type { DateTime } from 'luxon'

import type { FetchScorecardConfigurationWeightageData } from 'src/modules/scorecards/Settings/api/queries'

import type { FetchPeriodGroupScoresData } from '../../api/queries'
import type { EventCategory } from '../../api/types'
import { RiskEventDataGrid } from '../../components/RiskEventDataGrid'
import { eventCategoryOptions, transformRiskEventsData } from '../../components/utils'
import type { SelectedComparisonGroup } from '../../types'

export default function RiskEvents({
  dateRange,
  comparisonGroups,
  configData,
  apiData,
}: {
  dateRange: [DateTime, DateTime]
  configData: FetchScorecardConfigurationWeightageData
  comparisonGroups: ReadonlyArray<SelectedComparisonGroup>
  apiData: FetchPeriodGroupScoresData['riskEvents']
}) {
  const [eventCategoryFilter, setEventCategoryFilter] = useState<EventCategory>(
    eventCategoryOptions[0].value,
  )

  const isSingleGroup = comparisonGroups.length === 1

  const transformedData = useMemo(
    () =>
      transformRiskEventsData({
        apiData,
        comparisonGroups,
        configWeightageData: configData.weightageData,
        eventCategoryFilter,
        isSingleGroup,
      }),
    [apiData, comparisonGroups, configData, eventCategoryFilter, isSingleGroup],
  )

  return (
    <RiskEventDataGrid
      dateRange={dateRange}
      isSingleGroup={isSingleGroup}
      eventCategoryFilter={eventCategoryFilter}
      setEventCategoryFilter={setEventCategoryFilter}
      rowData={transformedData}
    />
  )
}
