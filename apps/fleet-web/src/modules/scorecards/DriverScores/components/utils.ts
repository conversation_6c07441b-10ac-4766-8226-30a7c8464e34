import { match } from 'ts-pattern'
import type { Except } from 'type-fest'

import { ctIntl } from 'src/util-components/ctIntl'

import { weightageApiKeyToFormPathMap } from '../../constants'
import type { FetchScorecardConfigurationWeightageData } from '../../Settings/api/queries'
import type { FetchPeriodGroupScoresData } from '../api/queries'
import type { EventCategory } from '../api/types'
import type { ChangeDirection } from '../Overview/types'
import type { SelectedComparisonGroup } from '../types'
import type { EventRow, GroupRow, RiskEventRow } from './types'

type GroupsData = FetchPeriodGroupScoresData['riskEvents']

type GroupData = Except<GroupsData[number], 'id'>

type CategoryData = GroupData['categories'][number]

type EventData = CategoryData['events'][number]

// Configuration for distance-based rates
export const RATE_DISTANCE_UNIT = 1000

/**
 * Formats seconds into a readable hours and minutes format using translations
 */
export function formatTimeInSecond(seconds: number): string {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.ceil((seconds - hours * 3600) / 60)

  return ctIntl.formatMessage(
    { id: 'units.timeFormat' },
    { values: { hours, minutes } },
  )
}

/**
 * Calculates change between current and previous values
 */
export const calculateChange = (
  current: number,
  previous: number,
): {
  value: number
  direction: ChangeDirection
} => {
  if (current === previous) {
    return { value: 0, direction: 'unchanged' }
  }

  const change = current - previous
  const direction = change > 0 ? 'increase' : 'decrease'
  return { value: Math.abs(change), direction }
}

/**
 * Transform data for single group mode (grouping by event category)
 */
// Extract common logic for calculating aggregated values
const calculateAggregatedValues = (
  categoryOrEvent: {
    scoreImpact: number | null
  } & (
    | {
        rateType: 'timeBasedRate'
        timeInSecond: number | null
        prevTimeInSecond: number | null
        percentage: number | null
        prevPercentage: number | null
      }
    | {
        rateType: 'frequencyBasedRate'
        eventCount: number | null
        prevEventCount: number | null
      }
  ),
  group: GroupData,
) =>
  match(categoryOrEvent)
    .with({ rateType: 'timeBasedRate' }, (cate) => ({
      countDisplay: cate.timeInSecond ? formatTimeInSecond(cate.timeInSecond) : null,
      countValue: cate.timeInSecond,
      prevCountValue: cate.prevTimeInSecond,
      totalScoreImpact: cate.scoreImpact,
      rateValue: cate.percentage,
      prevRateValue: cate.prevPercentage,
      rateDisplay: cate.percentage
        ? ctIntl.formatMessage(
            { id: 'scorecards.units.timeBasedRateDisplay' },
            { values: { value: cate.percentage.toFixed(2) } },
          )
        : null,
    }))
    .with({ rateType: 'frequencyBasedRate' }, (cate) => {
      const rateValue =
        cate.eventCount && group.distanceInKm > 0
          ? (cate.eventCount / group.distanceInKm) * RATE_DISTANCE_UNIT
          : null
      const prevRateValue =
        cate.prevEventCount && group.prevDistanceInKm > 0
          ? (cate.prevEventCount / group.prevDistanceInKm) * RATE_DISTANCE_UNIT
          : null

      return {
        countDisplay: cate.eventCount
          ? ctIntl.formatMessage(
              { id: 'scorecards.numberOfEvents' },
              { values: { size: cate.eventCount } },
            )
          : null,
        countValue: cate.eventCount,
        prevCountValue: cate.prevEventCount,
        totalScoreImpact: cate.scoreImpact,
        rateValue,
        prevRateValue,
        rateDisplay: rateValue
          ? ctIntl.formatMessage(
              { id: 'scorecards.units.distanceBasedRateDisplay' },
              {
                values: {
                  value: rateValue.toFixed(2),
                  distance: RATE_DISTANCE_UNIT.toLocaleString(),
                },
              },
            )
          : null,
      }
    })
    .exhaustive()

const generateCategoryRow = ({
  category,
  group,
  prefix,
  color,
  name,
}: {
  prefix: string
  category: CategoryData
  group: GroupData
  color: string
  name: string
}) => {
  const {
    countValue,
    countDisplay,
    prevCountValue,
    totalScoreImpact,
    rateDisplay,
    rateValue,
    prevRateValue,
  } = calculateAggregatedValues(category, group)

  const countChange = calculateChange(countValue ?? 0, prevCountValue ?? 0)
  const rateChange = calculateChange(rateValue ?? 0, prevRateValue ?? 0)

  const groupRow: GroupRow = {
    id: prefix,
    path: [prefix],
    type: 'group',
    eventName: name,
    color,
    count: {
      metric: { display: countDisplay, change: countChange },
      type: category.rateType,
    },
    rate: {
      metric: { display: rateDisplay, change: rateChange },
      type: category.rateType,
    },
    scoreImpact: totalScoreImpact,
  }

  return groupRow
}

const generateCategoryEventRows = ({
  events,
  group,
  prefix,
  configWeightageData,
}: {
  prefix: string
  events: Array<EventData>
  group: GroupData
  configWeightageData: FetchScorecardConfigurationWeightageData['weightageData']
}) => {
  const result = []
  for (const event of events) {
    // if related config is not enabled, skip
    const relatedWeightageFormPath = weightageApiKeyToFormPathMap.find(
      (item) => item.apiKey.toUpperCase() === event.eventType,
    )?.formPath
    if (
      !relatedWeightageFormPath ||
      !configWeightageData[relatedWeightageFormPath] ||
      !configWeightageData[relatedWeightageFormPath].enabled
    ) {
      continue
    }

    const {
      countValue,
      countDisplay,
      prevCountValue,
      rateDisplay,
      rateValue,
      prevRateValue,
    } = calculateAggregatedValues(event, group)

    const countChange = calculateChange(countValue ?? 0, prevCountValue ?? 0)
    const rateChange = calculateChange(rateValue ?? 0, prevRateValue ?? 0)

    const eventRow: EventRow = {
      id: prefix + `-event-${event.eventName.replace(/\s+/g, '-').toLowerCase()}`,
      path: [
        prefix,
        prefix + `-event-${event.eventName.replace(/\s+/g, '-').toLowerCase()}`,
      ],
      type: 'event',
      eventName: event.eventName,
      category: event.category,
      count: {
        value: countValue,
        previousValue: prevCountValue,
        display: countDisplay,
        change: countChange,
      },
      rate: {
        value: rateValue,
        previousValue: prevRateValue,
        display: rateDisplay,
        rateType: event.rateType,
        change: rateChange,
      },
      scoreImpact: event.scoreImpact,
    }

    result.push(eventRow)
  }
  return result
}

export function transformRiskEventsDataWithSingleGroup(
  singleGroup: GroupData,
  configWeightageData: FetchScorecardConfigurationWeightageData['weightageData'],
) {
  let result: Array<RiskEventRow> = []

  // Create group rows for each category
  for (const category of singleGroup.categories) {
    const events = category.events
    if (events.length === 0) continue

    const categoryOption = eventCategoryOptions.find(
      (option) => option.value === category.type,
    )
    const categoryDisplayName = categoryOption?.label || category.type
    const categoryIdPrefix = `category-${category.type}`

    const categoryRow = generateCategoryRow({
      category,
      group: singleGroup,
      name: categoryDisplayName,
      color: 'transparent',
      prefix: categoryIdPrefix,
    })

    result.push(categoryRow)

    const eventRows = generateCategoryEventRows({
      events: category.events,
      group: singleGroup,
      prefix: categoryIdPrefix,
      configWeightageData,
    })

    result = result.concat(eventRows)
  }

  return result
}

function transformRiskEventsDataWithMultiGroups({
  groupData,
  configWeightageData,
  selectedGroups,
  eventCategoryFilter,
}: {
  groupData: GroupsData
  configWeightageData: FetchScorecardConfigurationWeightageData['weightageData']
  selectedGroups: ReadonlyArray<SelectedComparisonGroup>
  eventCategoryFilter: EventCategory
}) {
  let result: Array<RiskEventRow> = []

  const selectedGroupsMap = new Map<string, SelectedComparisonGroup>()
  for (const group of selectedGroups) {
    selectedGroupsMap.set(group.id, group)
  }

  for (const group of groupData) {
    const selectedCategory = group.categories.find(
      (category) => category.type === eventCategoryFilter,
    )

    if (!selectedCategory) continue

    const groupIdPrefix = `group-${group.id}`
    const groupName = selectedGroups.find((g) => g.id === group.id)?.name ?? ''
    const selectedGroup = selectedGroupsMap.get(group.id)
    const groupColor = selectedGroup?.color ?? 'black'

    const groupRow = generateCategoryRow({
      category: selectedCategory,
      group,
      name: groupName,
      color: groupColor,
      prefix: groupIdPrefix,
    })

    result.push(groupRow)

    const eventRows = generateCategoryEventRows({
      events: selectedCategory.events,
      group,
      prefix: groupIdPrefix,
      configWeightageData,
    })

    result = result.concat(eventRows)
  }

  return result
}

/**
 * Main function that transforms risk events data based on mode and inputs
 */
export type TransformRiskEventsDataParams = {
  apiData: GroupsData
  configWeightageData: FetchScorecardConfigurationWeightageData['weightageData']
  comparisonGroups: ReadonlyArray<SelectedComparisonGroup>
  eventCategoryFilter: EventCategory
  isSingleGroup: boolean
}

export function transformRiskEventsData({
  apiData,
  comparisonGroups,
  configWeightageData,
  eventCategoryFilter,
  isSingleGroup,
}: TransformRiskEventsDataParams): Array<RiskEventRow> {
  if (!apiData || apiData.length === 0) return []

  return isSingleGroup
    ? transformRiskEventsDataWithSingleGroup(apiData[0], configWeightageData)
    : transformRiskEventsDataWithMultiGroups({
        groupData: apiData,
        configWeightageData,
        selectedGroups: comparisonGroups,
        eventCategoryFilter,
      })
}

export const eventCategoryOptions: Array<{
  value: EventCategory
  label: string
}> = [
  {
    value: 'speeding',
    label: 'scoreCards.settings.weightage.speeding.title',
  },
  {
    value: 'harsh_driving',
    label: 'scoreCards.settings.weightage.harsh.title',
  },
  {
    value: 'ai_camera_events',
    label: 'scoreCards.settings.weightage.aiCamera.title',
  },
  {
    value: 'ai_collision_events',
    label: 'scoreCards.settings.weightage.aiCollision.title',
  },
  {
    value: 'efficiency_events',
    label: 'scoreCards.settings.weightage.driverBehaviour.title',
  },
] as const
