import type { EventCategory } from '../api/types'
import type { ChangeDirection } from '../Overview/types'

export type RateType = 'timeBasedRate' | 'frequencyBasedRate'

export type MetricDisplayMetadata = {
  display: string | null
  change: {
    value: number
    direction: ChangeDirection
  }
}

export type BaseRiskEventRow = {
  id: string
  path: Array<string>
  eventName: string
}

export type GroupMetric = {
  metric: MetricDisplayMetadata
  type: RateType
}

export type GroupRow = BaseRiskEventRow & {
  type: 'group'
  count: GroupMetric
  rate: GroupMetric
  scoreImpact: number | null
  color: string
}

export type EventRow = BaseRiskEventRow & {
  type: 'event'
  count: {
    value: number | null
    previousValue: number | null
    display: string | null
    change: {
      value: number
      direction: ChangeDirection
    }
  }
  rate: {
    value: number | null
    previousValue: number | null
    display: string | null
    rateType: RateType
    change: {
      value: number
      direction: ChangeDirection
    }
  }
  scoreImpact: number | null
  category: EventCategory
}

export type RiskEventRow = GroupRow | EventRow
